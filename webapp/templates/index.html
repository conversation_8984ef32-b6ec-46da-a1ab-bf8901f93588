<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Voice Calling Application</title>
    <link rel="icon" type="image/svg+xml" href="{{ url_for('static', filename='favicon.svg') }}">
    <script>
        // Suppress production warning
        window.tailwindDevMode = true;
    </script>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/styles.css') }}">
    <meta http-equiv="Content-Security-Policy" content="default-src 'self' https:; script-src 'self' 'unsafe-inline' https://cdn.tailwindcss.com; style-src 'self' 'unsafe-inline' https:; connect-src 'self' ws: wss:;">
    <style>
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 8px;
        }
        .status-connected {
            background-color: #10b981;
            box-shadow: 0 0 8px rgba(16, 185, 129, 0.6);
        }
        .status-disconnected {
            background-color: #ef4444;
            box-shadow: 0 0 8px rgba(239, 68, 68, 0.6);
        }
        .status-connecting {
            background-color: #f59e0b;
            box-shadow: 0 0 8px rgba(245, 158, 11, 0.6);
            animation: pulse 1.5s infinite;
        }
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
        .audio-level {
            height: 4px;
            background: linear-gradient(to right, #10b981, #f59e0b, #ef4444);
            border-radius: 2px;
            transition: width 0.1s ease-in-out;
        }
        .debug-console {
            font-family: 'Courier New', monospace;
            font-size: 12px;
            line-height: 1.4;
            max-height: 200px;
            overflow-y: auto;
        }
        .log-entry {
            margin: 2px 0;
            padding: 2px 4px;
            border-radius: 2px;
        }
        .log-info { background-color: #e0f2fe; color: #0277bd; }
        .log-warning { background-color: #fff3e0; color: #f57c00; }
        .log-error { background-color: #ffebee; color: #d32f2f; }
        .log-success { background-color: #e8f5e8; color: #2e7d32; }
    </style>
</head>
<body class="bg-gray-100 min-h-screen">
    <div class="container mx-auto px-4 py-8 max-w-4xl">
        <!-- Header -->
        <div class="text-center mb-8">
            <h1 class="text-3xl font-bold text-gray-800 mb-2">Voice Calling Application</h1>
            <p class="text-gray-600">Real-time voice communication with AI backend</p>
        </div>

        <!-- Main Control Panel -->
        <div class="bg-white rounded-lg shadow-lg p-6 mb-6">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Call Controls -->
                <div class="space-y-4">
                    <h2 class="text-xl font-semibold text-gray-800 mb-4">Call Controls</h2>
                    
                    <!-- Status Display -->
                    <div class="flex items-center p-3 bg-gray-50 rounded-lg">
                        <span class="status-indicator status-disconnected" id="statusIndicator"></span>
                        <span class="font-medium text-gray-700">Status: </span>
                        <span id="statusText" class="ml-2 font-semibold text-red-600">Disconnected</span>
                    </div>

                    <!-- Call Buttons -->
                    <div class="space-y-3">
                        <div class="flex space-x-3">
                            <button id="startCallBtn" 
                                    class="flex-1 bg-green-600 hover:bg-green-700 disabled:bg-gray-400 disabled:cursor-not-allowed text-white font-semibold py-3 px-6 rounded-lg transition-colors duration-200">
                                Start Call
                            </button>
                            <button id="stopCallBtn" 
                                    class="flex-1 bg-red-600 hover:bg-red-700 disabled:bg-gray-400 disabled:cursor-not-allowed text-white font-semibold py-3 px-6 rounded-lg transition-colors duration-200" 
                                    disabled>
                                Stop Call
                            </button>
                        </div>
                        <div class="flex space-x-2">
                            <button id="reconnectBtn" 
                                    class="flex-1 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed text-white font-semibold py-2 px-4 rounded-lg transition-colors duration-200 text-sm">
                                Reconnect to Backend
                            </button>
                            <button id="testMicBtn" 
                                    class="flex-1 bg-purple-600 hover:bg-purple-700 disabled:bg-gray-400 disabled:cursor-not-allowed text-white font-semibold py-2 px-4 rounded-lg transition-colors duration-200 text-sm">
                                Test Microphone
                            </button>
                        </div>
                        <button id="testAudioBtn"
                                class="w-full bg-orange-600 hover:bg-orange-700 disabled:bg-gray-400 disabled:cursor-not-allowed text-white font-semibold py-2 px-4 rounded-lg transition-colors duration-200 text-sm mb-2">
                            Test Audio Playback
                        </button>
                        <button id="testClarityBtn"
                                class="w-full bg-green-600 hover:bg-green-700 disabled:bg-gray-400 disabled:cursor-not-allowed text-white font-semibold py-2 px-4 rounded-lg transition-colors duration-200 text-sm">
                            Test Audio Clarity
                        </button>
                    </div>

                    <!-- Audio Level Indicator -->
                    <div class="p-3 bg-gray-50 rounded-lg">
                        <div class="flex items-center justify-between mb-2">
                            <span class="text-sm font-medium text-gray-700">Microphone Level</span>
                            <span id="audioLevelText" class="text-sm text-gray-500">0%</span>
                        </div>
                        <div class="w-full bg-gray-200 rounded-full h-2">
                            <div id="audioLevelBar" class="audio-level" style="width: 0%"></div>
                        </div>
                    </div>
                </div>

                <!-- Connection Info -->
                <div class="space-y-4">
                    <h2 class="text-xl font-semibold text-gray-800 mb-4">Connection Info</h2>
                    
                    <div class="space-y-3">
                        <div class="flex justify-between p-2 bg-gray-50 rounded">
                            <span class="text-gray-600">WebSocket:</span>
                            <span id="wsStatus" class="font-medium text-red-600">Disconnected</span>
                        </div>
                        <div class="flex justify-between p-2 bg-gray-50 rounded">
                            <span class="text-gray-600">Audio Format:</span>
                            <span class="font-medium text-gray-800">16-bit PCM, 8kHz</span>
                        </div>
                        <div class="flex justify-between p-2 bg-gray-50 rounded">
                            <span class="text-gray-600">Backend:</span>
                            <span class="font-medium text-gray-800">localhost:5010</span>
                        </div>
                        <div class="flex justify-between p-2 bg-gray-50 rounded">
                            <span class="text-gray-600">Session ID:</span>
                            <span id="sessionId" class="font-medium text-gray-800">-</span>
                        </div>
                    </div>

                    <!-- Stats -->
                    <div class="mt-4">
                        <h3 class="text-sm font-medium text-gray-700 mb-2">Session Stats</h3>
                        <div class="grid grid-cols-2 gap-2 text-sm">
                            <div class="text-center p-2 bg-blue-50 rounded">
                                <div class="font-bold text-blue-600" id="packetsSent">0</div>
                                <div class="text-blue-600">Packets Sent</div>
                            </div>
                            <div class="text-center p-2 bg-green-50 rounded">
                                <div class="font-bold text-green-600" id="packetsReceived">0</div>
                                <div class="text-green-600">Packets Received</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Debug Console -->
        <div class="bg-white rounded-lg shadow-lg p-6">
            <div class="flex items-center justify-between mb-4">
                <h2 class="text-xl font-semibold text-gray-800">Debug Console</h2>
                <div class="flex space-x-2">
                    <button id="clearLogsBtn" 
                            class="px-3 py-1 text-sm bg-gray-200 hover:bg-gray-300 text-gray-700 rounded transition-colors">
                        Clear
                    </button>
                    <button id="toggleAutoScrollBtn" 
                            class="px-3 py-1 text-sm bg-blue-200 hover:bg-blue-300 text-blue-700 rounded transition-colors">
                        Auto-scroll: ON
                    </button>
                </div>
            </div>
            <div id="debugConsole" class="debug-console bg-gray-50 p-4 rounded border">
                <div class="log-entry log-info">Voice calling application initialized</div>
                <div class="log-entry log-info">Ready to connect to WebSocket backend</div>
            </div>
        </div>

        <!-- Browser Compatibility Notice -->
        <div id="compatibilityNotice" class="mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg hidden">
            <div class="flex items-center">
                <svg class="w-5 h-5 text-yellow-600 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                </svg>
                <div>
                    <h3 class="text-yellow-800 font-medium">Browser Compatibility Notice</h3>
                    <p class="text-yellow-700 text-sm mt-1">Some features may not be fully supported in your browser. For the best experience, please use a modern browser like Chrome, Firefox, or Edge.</p>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript Modules -->
    <script src="{{ url_for('static', filename='js/websocket-manager.js') }}"></script>
    <script src="{{ url_for('static', filename='js/voice-call-engine.js') }}"></script>
    <script src="{{ url_for('static', filename='js/ui-controller.js') }}"></script>
    
    <!-- Main Application Script -->
    <script>
        // Initialize the voice calling application
        document.addEventListener('DOMContentLoaded', function() {
            // Add a small delay to ensure all scripts are loaded
            setTimeout(() => {
                if (typeof VoiceCallEngine === 'undefined') {
                    console.error('VoiceCallEngine not loaded');
                    return;
                }
                if (typeof WebSocketManager === 'undefined') {
                    console.error('WebSocketManager not loaded');
                    return;
                }
                if (typeof UIController === 'undefined') {
                    console.error('UIController not loaded');
                    return;
                }
                
                const app = new VoiceCallApp();
                app.initialize();
            }, 100);
        });

        // Main application class
        class VoiceCallApp {
            constructor() {
                this.ui = null;
                this.engine = null;
                this.wsManager = null;
            }

            async initialize() {
                try {
                    // Check browser compatibility
                    this.checkBrowserCompatibility();

                    // Initialize UI controller
                    this.ui = new UIController();
                    
                    // Initialize WebSocket manager
                    this.wsManager = new WebSocketManager('ws://localhost:5010');
                    
                    // Initialize voice call engine
                    this.engine = new VoiceCallEngine(this.wsManager, this.ui);
                    
                    // Set up event listeners
                    this.setupEventListeners();
                    
                    this.ui.log('Application initialized successfully', 'success');
                    
                    // Auto-connect to WebSocket
                    this.connectToBackend();
                    
                } catch (error) {
                    console.error('Failed to initialize application:', error);
                    if (this.ui) {
                        this.ui.log(`Initialization failed: ${error.message}`, 'error');
                        this.ui.showError('Application initialization failed', error.message);
                    }
                }
            }

            async connectToBackend() {
                try {
                    this.ui.updateConnectionStatus('connecting');
                    this.ui.log('Connecting to WebSocket backend...', 'info');
                    await this.wsManager.connect();
                } catch (error) {
                    this.ui.log(`Failed to connect to backend: ${error.message}`, 'error');
                    this.ui.showError('Backend connection failed', 'Could not connect to WebSocket server. Please ensure the backend is running on port 5010.');
                }
            }

            async testMicrophone() {
                try {
                    this.ui.log('Testing microphone access...', 'info');
                    
                    const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
                    this.ui.log('✅ Microphone access granted', 'success');
                    
                    // Create a simple audio context test
                    const audioContext = new (window.AudioContext || window.webkitAudioContext)();
                    const source = audioContext.createMediaStreamSource(stream);
                    const analyser = audioContext.createAnalyser();
                    source.connect(analyser);
                    
                    const dataArray = new Uint8Array(analyser.frequencyBinCount);
                    
                    // Test for 3 seconds
                    let testDuration = 3000;
                    const testInterval = setInterval(() => {
                        analyser.getByteFrequencyData(dataArray);
                        const average = dataArray.reduce((a, b) => a + b) / dataArray.length;
                        this.ui.log(`Audio level: ${Math.round(average)}`, 'info');
                        
                        testDuration -= 500;
                        if (testDuration <= 0) {
                            clearInterval(testInterval);
                            stream.getTracks().forEach(track => track.stop());
                            audioContext.close();
                            this.ui.log('✅ Microphone test completed', 'success');
                        }
                    }, 500);
                    
                } catch (error) {
                    this.ui.log(`❌ Microphone test failed: ${error.message}`, 'error');
                    this.ui.showError('Microphone test failed', error.message);
                }
            }

            async testAudioPlayback() {
                try {
                    this.ui.log('Testing audio playback system...', 'info');

                    if (!this.engine || !this.engine.playbackContext) {
                        this.ui.log('Initializing audio engine for playback test...', 'info');
                        if (!this.engine.playbackContext) {
                            this.engine.playbackContext = new (window.AudioContext || window.webkitAudioContext)();
                            this.engine.playbackGain = this.engine.playbackContext.createGain();
                            this.engine.playbackGain.connect(this.engine.playbackContext.destination);
                        }
                    }

                    // Test 1: Generate a simple test tone using Web Audio API
                    const sampleRate = 44100;
                    const duration = 0.5; // 0.5 seconds
                    const frequency = 440; // A4 note
                    const samples = sampleRate * duration;

                    const audioBuffer = this.engine.playbackContext.createBuffer(1, samples, sampleRate);
                    const channelData = audioBuffer.getChannelData(0);

                    for (let i = 0; i < samples; i++) {
                        channelData[i] = Math.sin(2 * Math.PI * frequency * i / sampleRate) * 0.3;
                    }

                    // Play the test tone
                    const source = this.engine.playbackContext.createBufferSource();
                    source.buffer = audioBuffer;
                    source.connect(this.engine.playbackGain);
                    source.start();

                    this.ui.log('🔊 Playing test tone (440Hz, 0.5 second)', 'success');

                    // Test 2: Simulate backend audio format (44.1kHz, 16-bit PCM)
                    setTimeout(() => {
                        this.ui.log('Testing with simulated backend audio (44.1kHz, 16-bit PCM)...', 'info');

                        // Generate multiple 512-byte chunks like backend sends
                        const chunkSize = 512; // bytes
                        const numChunks = 5; // Send 5 chunks
                        let chunkIndex = 0;

                        const sendChunk = () => {
                            if (chunkIndex >= numChunks) {
                                this.ui.log('✅ Backend audio simulation completed', 'success');
                                return;
                            }

                            // Generate 512 bytes of 16-bit PCM (256 samples)
                            const numSamples = chunkSize / 2;
                            const testData = new Uint8Array(chunkSize);
                            const frequency = 880 + (chunkIndex * 110); // Vary frequency per chunk

                            for (let i = 0; i < numSamples; i++) {
                                // Generate sine wave sample
                                const globalSampleIndex = (chunkIndex * numSamples) + i;
                                const sample = Math.sin(2 * Math.PI * frequency * globalSampleIndex / sampleRate) * 16000;
                                const intSample = Math.round(sample);

                                // Convert to 16-bit little-endian bytes
                                const byteIndex = i * 2;
                                testData[byteIndex] = intSample & 0xFF; // Low byte
                                testData[byteIndex + 1] = (intSample >> 8) & 0xFF; // High byte
                            }

                            // Convert to base64 like backend does
                            const testBase64 = btoa(String.fromCharCode.apply(null, testData));

                            if (this.engine) {
                                this.engine.isPlaying = true; // Enable playback
                                this.engine.handleIncomingAudio(testBase64);
                            }

                            chunkIndex++;
                            // Send next chunk after a short delay
                            setTimeout(sendChunk, 100);
                        };

                        sendChunk();

                    }, 1000);

                } catch (error) {
                    this.ui.log(`❌ Audio playback test failed: ${error.message}`, 'error');
                    this.ui.showError('Audio playback test failed', error.message);
                }
            }

            async testAudioClarity() {
                try {
                    this.ui.log('🎵 Starting audio clarity test...', 'info');

                    if (!this.wsManager.isConnected) {
                        this.ui.log('❌ Please connect to WebSocket first', 'error');
                        return;
                    }

                    if (!this.engine.isCallActive) {
                        this.ui.log('❌ Please start a call first', 'error');
                        return;
                    }

                    // Test phrases for clarity evaluation
                    const testPhrases = [
                        "Testing crystal clear speech quality.",
                        "The quick brown fox jumps over the lazy dog.",
                        "She sells seashells by the seashore.",
                        "Peter Piper picked a peck of pickled peppers."
                    ];

                    this.ui.log('🎤 Sending test phrases to AI...', 'info');

                    for (let i = 0; i < testPhrases.length; i++) {
                        const phrase = testPhrases[i];
                        this.ui.log(`📝 Test ${i+1}: "${phrase}"`, 'info');

                        // Simulate speaking the phrase by sending audio chunks
                        const testMessage = `Please repeat this clearly: ${phrase}`;

                        // Send dummy audio to trigger AI response
                        for (let j = 0; j < 10; j++) {
                            const dummyAudio = new Array(512).fill(128); // Silence
                            this.wsManager.sendAudioChunk(dummyAudio);
                            await new Promise(resolve => setTimeout(resolve, 50));
                        }

                        this.ui.log(`✅ Sent test phrase ${i+1}, listening for AI response...`, 'info');
                        await new Promise(resolve => setTimeout(resolve, 2000)); // Wait for response
                    }

                    this.ui.log('🎵 Audio clarity test completed!', 'success');
                    this.ui.log('👂 Listen carefully to the AI responses and check for:', 'info');
                    this.ui.log('   - Clear speech without distortion', 'info');
                    this.ui.log('   - Consistent volume levels', 'info');
                    this.ui.log('   - Reduced background noise', 'info');
                    this.ui.log('   - Natural speech quality', 'info');
                    this.ui.log('   - No clicks, pops, or artifacts', 'info');

                } catch (error) {
                    this.ui.log(`❌ Audio clarity test failed: ${error.message}`, 'error');
                    this.ui.showError('Audio clarity test failed', error.message);
                }
            }

            setupEventListeners() {
                document.getElementById('startCallBtn').addEventListener('click', () => {
                    this.engine.startCall();
                });

                document.getElementById('stopCallBtn').addEventListener('click', () => {
                    this.engine.stopCall();
                });

                document.getElementById('reconnectBtn').addEventListener('click', () => {
                    this.connectToBackend();
                });

                document.getElementById('testMicBtn').addEventListener('click', () => {
                    this.testMicrophone();
                });

                document.getElementById('testAudioBtn').addEventListener('click', () => {
                    this.testAudioPlayback();
                });

                document.getElementById('testClarityBtn').addEventListener('click', () => {
                    this.testAudioClarity();
                });

                document.getElementById('clearLogsBtn').addEventListener('click', () => {
                    this.ui.clearLogs();
                });

                document.getElementById('toggleAutoScrollBtn').addEventListener('click', () => {
                    this.ui.toggleAutoScroll();
                });
            }

            checkBrowserCompatibility() {
                const hasWebAudio = !!(window.AudioContext || window.webkitAudioContext);
                const hasWebSocket = !!window.WebSocket;
                const hasGetUserMedia = !!(navigator.mediaDevices && navigator.mediaDevices.getUserMedia);

                if (!hasWebAudio || !hasWebSocket || !hasGetUserMedia) {
                    document.getElementById('compatibilityNotice').classList.remove('hidden');
                    
                    const missing = [];
                    if (!hasWebAudio) missing.push('Web Audio API');
                    if (!hasWebSocket) missing.push('WebSocket');
                    if (!hasGetUserMedia) missing.push('getUserMedia');
                    
                    throw new Error(`Missing browser features: ${missing.join(', ')}`);
                }
            }
        }
    </script>
</body>
</html>