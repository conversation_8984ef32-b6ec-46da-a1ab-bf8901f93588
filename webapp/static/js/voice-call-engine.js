/**
 * Voice Call Engine for Real-time Audio Processing
 * Handles microphone capture, audio streaming, and playback
 */
class VoiceCallEngine {
    constructor(webSocketManager, uiController) {
        this.wsManager = webSocketManager;
        this.ui = uiController;
        
        // Audio context and nodes
        this.audioContext = null;
        this.mediaStream = null;
        this.sourceNode = null;
        this.analyserNode = null;
        this.gainNode = null;
        this.scriptProcessor = null;
        
        // Audio playback
        this.playbackContext = null;
        this.playbackGain = null;
        
        // Audio processing  
        this.sampleRate = 8000; // Target sample rate for backend
        this.bufferSize = 1024; // Smaller buffer for more frequent chunks
        this.isRecording = false;
        this.isPlaying = false;
        
        // Enhanced audio buffering for consistent streaming
        this.audioBuffer = [];
        this.bufferThreshold = 1024; // Send chunks of 1024 samples
        this.playbackQueue = [];
        this.isProcessingQueue = false;
        this.bufferStartTime = null;
        this.maxBufferSize = 8; // Maximum buffer size to prevent memory issues
        
        // AudioWorklet processor
        this.audioWorkletNode = null;
        this.scriptProcessor = null; // Fallback for older browsers
        
        // Audio level monitoring
        this.audioLevelCallback = null;
        this.lastAudioLevel = 0;
        
        // Call state
        this.isCallActive = false;
        this.callStartTime = null;
        
        // Bind methods
        this.handleIncomingAudio = this.handleIncomingAudio.bind(this);
        
        // Set up WebSocket event handlers
        this.setupWebSocketHandlers();
    }

    /**
     * Set up WebSocket event handlers
     */
    setupWebSocketHandlers() {
        this.wsManager.on('audio-data', this.handleIncomingAudio);
        this.wsManager.on('connected', () => {
            this.ui.updateConnectionStatus('connected');
            this.ui.updateWebSocketInfo(this.wsManager.getStatus());
        });
        this.wsManager.on('disconnected', () => {
            this.ui.updateConnectionStatus('disconnected');
            if (this.isCallActive) {
                this.stopCall();
            }
        });
        this.wsManager.on('stats-update', (stats) => {
            this.ui.updateStats(stats);
        });
        this.wsManager.on('log', (message, type) => {
            this.ui.log(message, type);
        });
        this.wsManager.on('error', (error) => {
            this.ui.log(`WebSocket error: ${error.message || 'Unknown error'}`, 'error');
        });
    }

    /**
     * Initialize audio context and prepare for recording
     */
    async initializeAudio() {
        try {
            // Create audio context with optimal settings
            const AudioContextClass = window.AudioContext || window.webkitAudioContext;

            // Recording context for microphone input
            this.audioContext = new AudioContextClass({
                sampleRate: 44100, // Match backend sample rate for consistency
                latencyHint: 'interactive'
            });

            // Separate playback context to avoid feedback and optimize for audio quality
            this.playbackContext = new AudioContextClass({
                sampleRate: 44100, // Match backend output sample rate
                latencyHint: 'playback' // Optimize for audio quality over latency
            });

            // Create optimized playback gain with proper volume control
            this.playbackGain = this.playbackContext.createGain();
            this.playbackGain.connect(this.playbackContext.destination);
            this.playbackGain.gain.value = 1.0; // Increased volume for better clarity

            // Add a gentle compressor to prevent distortion while preserving speech clarity
            this.compressor = this.playbackContext.createDynamicsCompressor();
            this.compressor.threshold.value = -12; // Higher threshold for less compression
            this.compressor.knee.value = 15; // Softer knee for smoother compression
            this.compressor.ratio.value = 3; // Lower ratio for more natural sound
            this.compressor.attack.value = 0.005; // Slightly slower attack to preserve transients
            this.compressor.release.value = 0.25; // Longer release for smoother sound

            // Add a gentle high-pass filter to remove very low frequencies only
            this.highPassFilter = this.playbackContext.createBiquadFilter();
            this.highPassFilter.type = 'highpass';
            this.highPassFilter.frequency.value = 60; // Remove only very low frequencies
            this.highPassFilter.Q.value = 0.5; // Gentler slope

            // Add a gentle low-pass filter to remove harsh high frequencies
            this.lowPassFilter = this.playbackContext.createBiquadFilter();
            this.lowPassFilter.type = 'lowpass';
            this.lowPassFilter.frequency.value = 12000; // Keep more speech frequencies
            this.lowPassFilter.Q.value = 0.5; // Gentler slope

            // Add a speech enhancement filter (peaking filter for speech clarity)
            this.speechEnhancer = this.playbackContext.createBiquadFilter();
            this.speechEnhancer.type = 'peaking';
            this.speechEnhancer.frequency.value = 2000; // Enhance speech frequencies around 2kHz
            this.speechEnhancer.Q.value = 1.0; // Moderate Q for natural sound
            this.speechEnhancer.gain.value = 2; // Gentle boost for clarity

            // Connect audio processing chain: highpass -> lowpass -> speech enhancer -> compressor -> gain
            this.highPassFilter.connect(this.lowPassFilter);
            this.lowPassFilter.connect(this.speechEnhancer);
            this.speechEnhancer.connect(this.compressor);
            this.compressor.connect(this.playbackGain);

            this.ui.log('Audio contexts initialized with optimization', 'success');

            // Calculate downsample ratio for recording
            this.downsampleRatio = this.audioContext.sampleRate / this.sampleRate;
            this.ui.log(`Recording sample rate: ${this.audioContext.sampleRate}Hz -> ${this.sampleRate}Hz`, 'info');
            this.ui.log(`Playback sample rate: ${this.playbackContext.sampleRate}Hz (optimized for backend)`, 'info');

        } catch (error) {
            this.ui.log(`Audio initialization failed: ${error.message}`, 'error');
            throw error;
        }
    }

    /**
     * Request microphone access and set up audio processing
     */
    async setupMicrophone() {
        try {
            // Request microphone access with better constraints
            this.mediaStream = await navigator.mediaDevices.getUserMedia({
                audio: {
                    echoCancellation: true,
                    noiseSuppression: true,
                    autoGainControl: true,
                    sampleRate: 48000, // High quality input
                    channelCount: 1
                }
            });

            // Create audio nodes
            this.sourceNode = this.audioContext.createMediaStreamSource(this.mediaStream);
            this.analyserNode = this.audioContext.createAnalyser();
            this.gainNode = this.audioContext.createGain();
            
            // Configure analyser for audio level monitoring
            this.analyserNode.fftSize = 256;
            this.analyserNode.smoothingTimeConstant = 0.3;
            
            // Try to use AudioWorklet, fallback to ScriptProcessor
            try {
                if (this.audioContext.audioWorklet) {
                    // Load AudioWorklet processor
                    await this.audioContext.audioWorklet.addModule('/static/js/audio-worklet-processor.js');
                    
                    // Create AudioWorklet node
                    this.audioWorkletNode = new AudioWorkletNode(this.audioContext, 'audio-processor');
                    
                    // Handle messages from AudioWorklet
                    this.audioWorkletNode.port.onmessage = (event) => {
                        if (event.data.type === 'audioData' && this.isRecording) {
                            this.bufferAndSendAudio(event.data.data);
                        }
                    };
                    
                    // Send sample rate to worklet
                    this.audioWorkletNode.port.postMessage({
                        type: 'setSampleRate',
                        sampleRate: this.audioContext.sampleRate
                    });
                    
                    // Connect with AudioWorklet
                    this.sourceNode.connect(this.analyserNode);
                    this.analyserNode.connect(this.gainNode);
                    this.gainNode.connect(this.audioWorkletNode);
                    this.audioWorkletNode.connect(this.audioContext.destination);
                    
                    this.ui.log('Using AudioWorklet for audio processing', 'success');
                } else {
                    throw new Error('AudioWorklet not supported');
                }
            } catch (error) {
                // Fallback to ScriptProcessor
                this.ui.log('AudioWorklet failed, using ScriptProcessor fallback', 'warning');
                
                this.scriptProcessor = this.audioContext.createScriptProcessor(this.bufferSize, 1, 1);
                this.scriptProcessor.onaudioprocess = (event) => {
                    if (!this.isRecording) return;
                    
                    const inputData = event.inputBuffer.getChannelData(0);
                    
                    // Simple audio processing and buffering
                    const downsampledData = this.downsampleAudio(inputData);
                    const pcmData = this.convertToPCM8(downsampledData);
                    
                    this.bufferAndSendAudio(pcmData);
                };
                
                // Connect with ScriptProcessor
                this.sourceNode.connect(this.analyserNode);
                this.analyserNode.connect(this.gainNode);
                this.gainNode.connect(this.scriptProcessor);
                this.scriptProcessor.connect(this.audioContext.destination);
            }
            
            // Set gain for optimal audio capture (higher than before)
            this.gainNode.gain.value = 0.1;
            
            this.ui.log('Microphone setup completed with AudioWorklet', 'success');
            
            // Start audio level monitoring
            this.startAudioLevelMonitoring();
            
        } catch (error) {
            this.ui.log(`Microphone setup failed: ${error.message}`, 'error');
            throw error;
        }
    }

    /**
     * Start audio level monitoring
     */
    startAudioLevelMonitoring() {
        const updateAudioLevel = () => {
            if (!this.analyserNode) return;
            
            const dataArray = new Uint8Array(this.analyserNode.frequencyBinCount);
            this.analyserNode.getByteFrequencyData(dataArray);
            
            // Calculate RMS level
            let sum = 0;
            for (let i = 0; i < dataArray.length; i++) {
                sum += dataArray[i] * dataArray[i];
            }
            const rms = Math.sqrt(sum / dataArray.length);
            const level = Math.min(100, (rms / 128) * 100);
            
            this.lastAudioLevel = level;
            this.ui.updateAudioLevel(level);
            
            if (this.isRecording) {
                requestAnimationFrame(updateAudioLevel);
            }
        };
        
        updateAudioLevel();
    }

    /**
     * Downsample audio data to target sample rate
     */
    downsampleAudio(inputData) {
        const downsampleRatio = this.audioContext.sampleRate / this.sampleRate;
        const outputLength = Math.ceil(inputData.length / downsampleRatio);
        const outputData = new Float32Array(outputLength);
        
        for (let i = 0; i < outputLength; i++) {
            const sourceIndex = Math.floor(i * downsampleRatio);
            outputData[i] = inputData[sourceIndex];
        }
        
        return outputData;
    }

    /**
     * Convert float audio data to 8-bit PCM (backend expects 8-bit!)
     */
    convertToPCM8(floatData) {
        const pcmData = [];
        
        for (let i = 0; i < floatData.length; i++) {
            // Apply gain boost for better AI processing
            let sample = floatData[i] * 3.0; // Boost signal by 3x
            // Clamp to [-1, 1] and convert to 8-bit unsigned (0-255)
            sample = Math.max(-1, Math.min(1, sample));
            // Convert from [-1, 1] to [0, 255]
            const unsignedSample = Math.round((sample + 1) * 127.5);
            pcmData.push(unsignedSample);
        }
        
        return pcmData; // Return regular array, not typed array
    }

    /**
     * Buffer audio data and send in larger chunks like mobile app
     */
    bufferAndSendAudio(pcmData) {
        // Add new audio data to buffer
        this.audioBuffer.push(...pcmData);
        
        // Send chunks when buffer reaches threshold
        while (this.audioBuffer.length >= this.bufferThreshold) {
            const chunk = this.audioBuffer.splice(0, this.bufferThreshold);
            
            // Check if chunk has sufficient audio signal (not just silence)
            const avgLevel = chunk.reduce((sum, val) => sum + Math.abs(val - 127), 0) / chunk.length;
            
            // Send all audio chunks with even minimal activity (like mobile app)
            if (avgLevel > 0.5) { // Very low threshold - send almost everything
                try {
                    this.wsManager.sendAudioChunk(chunk);
                    // Log occasionally to avoid spam
                    if (Math.random() < 0.05) {
                        this.ui.log(`Sent buffered audio chunk: ${chunk.length} samples, level: ${avgLevel.toFixed(1)}`, 'info');
                    }
                } catch (error) {
                    this.ui.log(`Error sending audio data: ${error.message}`, 'error');
                }
            }
        }
    }

    /**
     * Handle incoming audio data from backend
     */
    handleIncomingAudio(audioData) {
        if (!this.isPlaying || !this.playbackContext) {
            this.ui.log('Playback not ready - skipping audio', 'warning');
            return;
        }

        try {
            let decodedBytes = null;

            if (typeof audioData === 'string' && audioData.length > 0) {
                // Decode base64 to get raw audio bytes
                try {
                    const binaryString = atob(audioData);
                    decodedBytes = new Uint8Array(binaryString.length);
                    for (let i = 0; i < binaryString.length; i++) {
                        decodedBytes[i] = binaryString.charCodeAt(i);
                    }

                    // Log occasionally to avoid spam
                    if (Math.random() < 0.1) {
                        this.ui.log(`Received ${decodedBytes.length} bytes of audio data`, 'info');
                    }

                } catch (decodeError) {
                    this.ui.log(`Base64 decode error: ${decodeError.message}`, 'error');
                    return;
                }

            } else if (Array.isArray(audioData) && audioData.length > 0) {
                // Direct audio data as array
                decodedBytes = new Uint8Array(audioData);
                this.ui.log(`Received direct audio array: ${decodedBytes.length} bytes`, 'info');

            } else {
                this.ui.log(`Invalid audio data: ${typeof audioData}, length: ${audioData?.length || 'undefined'}`, 'warning');
                return;
            }

            if (!decodedBytes || decodedBytes.length === 0) {
                this.ui.log('No audio data to play', 'warning');
                return;
            }

            // Add to playback queue for smoother playback
            this.playbackQueue.push(decodedBytes);

            // Limit queue size to prevent memory issues
            if (this.playbackQueue.length > this.maxBufferSize) {
                this.playbackQueue.shift(); // Remove oldest chunk
                this.ui.log('Audio buffer overflow - dropping old chunk', 'warning');
            }

            // Process queue if not already processing
            if (!this.isProcessingQueue) {
                this.processAudioQueue();
            }

        } catch (error) {
            this.ui.log(`Audio playback error: ${error.message}`, 'error');
            console.error('Detailed audio error:', error);
        }
    }

    /**
     * Process audio queue for smooth playback
     */
    async processAudioQueue() {
        if (this.isProcessingQueue || this.playbackQueue.length === 0) {
            return;
        }

        this.isProcessingQueue = true;

        try {
            while (this.playbackQueue.length > 0 && this.isPlaying) {
                const audioChunk = this.playbackQueue.shift();

                // Play the audio chunk immediately for low latency
                this.playPCMAudio(audioChunk);

                // Reduced delay for better real-time playback
                // Calculate actual chunk duration for precise timing
                const chunkDurationMs = (audioChunk.length / 2) / 44.1; // Actual duration in ms
                const delayMs = Math.max(2, chunkDurationMs * 0.6); // 60% of chunk duration for overlap

                await new Promise(resolve => setTimeout(resolve, delayMs));
            }
        } catch (error) {
            this.ui.log(`Queue processing error: ${error.message}`, 'error');
        } finally {
            this.isProcessingQueue = false;

            // If more chunks arrived while processing, continue
            if (this.playbackQueue.length > 0 && this.isPlaying) {
                setTimeout(() => this.processAudioQueue(), 10);
            }
        }
    }



    /**
     * Play PCM audio data from backend (44.1kHz, 16-bit, mono, raw PCM)
     * Optimized for clear audio playback with proper format conversion and quality enhancements
     */
    playPCMAudio(decodedBytes) {
        try {
            // Validate input
            if (!decodedBytes || decodedBytes.length === 0) {
                this.ui.log('Empty audio data received', 'warning');
                return;
            }

            // Backend sends 44.1kHz, 16-bit, mono PCM as raw bytes
            const sampleRate = 44100;
            const bytesPerSample = 2; // 16-bit = 2 bytes per sample
            const numSamples = Math.floor(decodedBytes.length / bytesPerSample);

            // Skip chunks that are too small to be meaningful (adjusted for larger chunks)
            if (numSamples < 50) {
                this.ui.log(`Skipping tiny audio chunk: ${numSamples} samples`, 'debug');
                return;
            }

            // Ensure playback context is ready
            if (this.playbackContext.state === 'suspended') {
                this.playbackContext.resume();
            }

            // Create audio buffer
            const audioBuffer = this.playbackContext.createBuffer(1, numSamples, sampleRate);
            const channelData = audioBuffer.getChannelData(0);

            // Convert 16-bit signed little-endian PCM to float32 samples with improved clarity
            let maxAmplitude = 0;
            let rmsSum = 0;

            for (let i = 0; i < numSamples; i++) {
                const byteIndex = i * bytesPerSample;
                if (byteIndex + 1 < decodedBytes.length) {
                    // Little-endian: low byte first, then high byte
                    const lowByte = decodedBytes[byteIndex];
                    const highByte = decodedBytes[byteIndex + 1];

                    // Combine bytes to form 16-bit signed integer
                    let sample = (highByte << 8) | lowByte;

                    // Convert to signed 16-bit (two's complement)
                    if (sample >= 32768) {
                        sample -= 65536;
                    }

                    // Normalize to [-1, 1] range with proper scaling
                    const normalizedSample = sample / 32768.0;

                    // Store the sample directly without aggressive filtering for better clarity
                    channelData[i] = normalizedSample;

                    // Track maximum amplitude and RMS for dynamic range optimization
                    maxAmplitude = Math.max(maxAmplitude, Math.abs(normalizedSample));
                    rmsSum += normalizedSample * normalizedSample;
                }
            }

            // Calculate RMS level for better gain adjustment
            const rmsLevel = Math.sqrt(rmsSum / numSamples);

            // Apply gentle volume normalization for consistent levels
            if (rmsLevel > 0.001) { // Only normalize if there's actual audio content
                const targetRMS = 0.1; // Target RMS level for speech
                const normalizationFactor = Math.min(2.0, targetRMS / rmsLevel); // Limit max boost

                // Apply normalization to the audio data
                for (let i = 0; i < numSamples; i++) {
                    channelData[i] *= normalizationFactor;
                }

                // Apply simple noise gate to reduce low-level noise
                const noiseGateThreshold = 0.02; // Threshold below which audio is considered noise
                for (let i = 0; i < numSamples; i++) {
                    if (Math.abs(channelData[i]) < noiseGateThreshold) {
                        channelData[i] *= 0.1; // Reduce noise by 90%
                    }
                }

                // Apply simple de-clicking filter to remove audio artifacts
                for (let i = 1; i < numSamples - 1; i++) {
                    const current = channelData[i];
                    const prev = channelData[i - 1];
                    const next = channelData[i + 1];

                    // Detect sudden spikes (clicks/pops)
                    if (Math.abs(current) > 0.8 && Math.abs(prev) < 0.3 && Math.abs(next) < 0.3) {
                        // Replace spike with interpolated value
                        channelData[i] = (prev + next) * 0.5;
                    }
                }

                // Recalculate max amplitude after processing
                maxAmplitude = 0;
                for (let i = 0; i < numSamples; i++) {
                    maxAmplitude = Math.max(maxAmplitude, Math.abs(channelData[i]));
                }
            }

            // Create and configure audio source
            const source = this.playbackContext.createBufferSource();
            source.buffer = audioBuffer;

            // Improved dynamic gain adjustment for normalized audio
            const gainNode = this.playbackContext.createGain();

            // Since audio is now normalized, use simpler gain adjustment
            let dynamicGain;
            if (maxAmplitude > 0.8) {
                dynamicGain = 0.9; // Slight reduction for very loud normalized audio
            } else if (maxAmplitude > 0.3) {
                dynamicGain = 1.1; // Slight boost for normal normalized audio
            } else {
                dynamicGain = 1.3; // Moderate boost for quiet normalized audio
            }

            // Apply gentle fade-in to prevent clicks and pops
            gainNode.gain.setValueAtTime(0, this.playbackContext.currentTime);
            gainNode.gain.linearRampToValueAtTime(dynamicGain, this.playbackContext.currentTime + 0.005);

            // Connect through enhanced audio processing chain: source -> gain -> filters -> speech enhancer -> compressor -> output
            source.connect(gainNode);
            gainNode.connect(this.highPassFilter);

            // Start playback
            source.start();

            // Log occasionally for debugging with enhanced information
            if (Math.random() < 0.02) {
                this.ui.log(`🎵 Played ${numSamples} samples (${(numSamples/sampleRate*1000).toFixed(1)}ms, gain: ${dynamicGain.toFixed(1)}, max: ${maxAmplitude.toFixed(3)}, rms: ${rmsLevel.toFixed(3)})`, 'info');
            }

        } catch (error) {
            this.ui.log(`❌ Audio error: ${error.message}`, 'error');
            console.error('Audio playback error details:', error);
        }
    }

    /**
     * Helper function to convert two bytes to normalized sample
     */
    convertSample(lowByte, highByte) {
        let sample = (highByte << 8) | lowByte;
        if (sample >= 32768) {
            sample -= 65536;
        }
        return sample / 32768.0;
    }



    /**
     * Start a voice call
     */
    async startCall() {
        if (this.isCallActive) {
            this.ui.log('Call already active', 'warning');
            return;
        }
        
        try {
            this.ui.updateCallStatus('connecting');
            this.ui.log('Starting voice call...', 'info');
            
            // Connect to WebSocket if not connected
            if (!this.wsManager.isConnected) {
                await this.wsManager.connect();
            }
            
            // Initialize audio if not done
            if (!this.audioContext) {
                await this.initializeAudio();
            }
            
            // Set up microphone if not done
            if (!this.mediaStream) {
                await this.setupMicrophone();
            }
            
            // Resume audio contexts if suspended
            if (this.audioContext.state === 'suspended') {
                await this.audioContext.resume();
            }
            if (this.playbackContext.state === 'suspended') {
                await this.playbackContext.resume();
            }
            
            // Start call sequence like mobile app
            this.isCallActive = true;
            this.callStartTime = Date.now();
            
            // Reset audio buffer
            this.audioBuffer = [];
            
            // Notify backend and wait a moment for it to be ready
            this.wsManager.startAICall();
            await new Promise(resolve => setTimeout(resolve, 500)); // Wait 500ms
            
            this.wsManager.setAIListening(true);
            await new Promise(resolve => setTimeout(resolve, 200)); // Wait 200ms
            
            // Now start recording and playback
            this.isRecording = true;
            this.isPlaying = true;
            
            // Start audio level monitoring
            this.startAudioLevelMonitoring();
            
            // Debug: Check if audio processor is receiving audio
            setTimeout(() => {
                const processor = this.audioWorkletNode ? 'AudioWorklet' : 'ScriptProcessor';
                this.ui.log(`Audio processing status: recording=${this.isRecording}, using=${processor}`, 'info');
            }, 1000);
            
            this.ui.updateCallStatus('connected');
            this.ui.log('Voice call started successfully', 'success');
            this.ui.log('💡 Speak continuously for 3+ seconds for AI to respond', 'info');
            
            // Make HTTP request to Flask backend
            try {
                const response = await fetch('/start-call', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' }
                });
                const result = await response.json();
                this.ui.log(`Flask backend response: ${result.message}`, 'info');
            } catch (error) {
                this.ui.log(`Flask backend error: ${error.message}`, 'warning');
            }
            
        } catch (error) {
            this.ui.updateCallStatus('error');
            this.ui.log(`Failed to start call: ${error.message}`, 'error');
            this.stopCall();
        }
    }

    /**
     * Stop the voice call
     */
    async stopCall() {
        if (!this.isCallActive) {
            this.ui.log('No active call to stop', 'warning');
            return;
        }
        
        try {
            this.ui.log('Stopping voice call...', 'info');
            
            // Stop recording and playback
            this.isRecording = false;
            this.isPlaying = false;
            this.isCallActive = false;

            // Clear audio buffers and queue
            this.audioBuffer = [];
            this.playbackQueue = [];
            this.isProcessingQueue = false;
            
            // Notify backend
            if (this.wsManager.isConnected) {
                this.wsManager.endAICall();
            }
            
            // Stop media stream
            if (this.mediaStream) {
                this.mediaStream.getTracks().forEach(track => track.stop());
            }
            
            // Disconnect audio nodes
            if (this.audioWorkletNode) {
                this.audioWorkletNode.disconnect();
            }
            if (this.scriptProcessor) {
                this.scriptProcessor.disconnect();
            }
            if (this.sourceNode) {
                this.sourceNode.disconnect();
            }
            
            // Reset audio level
            this.ui.updateAudioLevel(0);
            
            this.ui.updateCallStatus('disconnected');
            this.ui.log('Voice call stopped', 'success');
            
            // Make HTTP request to Flask backend
            try {
                const response = await fetch('/end-call', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' }
                });
                const result = await response.json();
                this.ui.log(`Flask backend response: ${result.message}`, 'info');
            } catch (error) {
                this.ui.log(`Flask backend error: ${error.message}`, 'warning');
            }
            
        } catch (error) {
            this.ui.log(`Error stopping call: ${error.message}`, 'error');
        }
    }

    /**
     * Get call statistics
     */
    getCallStats() {
        return {
            isActive: this.isCallActive,
            duration: this.callStartTime ? Date.now() - this.callStartTime : 0,
            audioLevel: this.lastAudioLevel,
            sampleRate: this.sampleRate,
            bufferSize: this.bufferSize
        };
    }

    /**
     * Cleanup resources
     */
    cleanup() {
        this.stopCall();
        
        if (this.audioContext) {
            this.audioContext.close();
        }
        if (this.playbackContext) {
            this.playbackContext.close();
        }
        
        this.ui.log('Voice call engine cleaned up', 'info');
    }
}