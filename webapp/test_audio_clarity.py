#!/usr/bin/env python3
"""
Audio Clarity Test for WebApp
Tests the improved audio processing and playback quality
"""

import asyncio
import websockets
import json
import base64
import sys
import os

# Add the parent directory to the path to import modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    from tutor.modules.audio.text_speech import TextToSpeech
except ImportError:
    print("❌ Cannot import TTS module. Running simplified test without audio generation.")
    TextToSpeech = None

async def test_audio_clarity():
    """Test the improved audio clarity with various speech samples."""
    
    print("🎵 Audio Clarity Test for WebApp")
    print("=" * 50)
    
    # Test phrases with different characteristics
    test_phrases = [
        "Hello, this is a clear speech test.",
        "The quick brown fox jumps over the lazy dog.",
        "Testing consonants: <PERSON> picked a peck of pickled peppers.",
        "Testing vowels: How now brown cow, about the house.",
        "Testing sibilants: She sells seashells by the seashore.",
        "Testing numbers: One, two, three, four, five, six, seven, eight, nine, ten.",
        "Testing technical terms: Initialize the audio processing pipeline with enhanced clarity.",
        "Testing quiet speech: This is a very quiet whisper test.",
        "Testing loud speech: THIS IS A LOUD ANNOUNCEMENT TEST!",
        "Testing mixed content: The temperature is 25°C, that's about 77°F."
    ]
    
    if TextToSpeech:
        print("🔧 Step 1: Generating test audio samples...")
        tts = TextToSpeech()

        audio_samples = []
        for i, phrase in enumerate(test_phrases):
            print(f"   Generating sample {i+1}/10: '{phrase[:30]}...'")
            audio_stream, _ = tts.generate_audio(phrase)
            audio_stream.seek(0)
            audio_data = audio_stream.read()
            audio_b64 = base64.b64encode(audio_data).decode('utf-8')

            # Analyze audio characteristics
            sample_count = len(audio_data) // 2  # 16-bit samples
            duration_ms = (sample_count / 44100) * 1000

            audio_samples.append({
                'id': i + 1,
                'text': phrase,
                'audio': audio_b64,
                'size': len(audio_data),
                'samples': sample_count,
                'duration_ms': duration_ms
            })
            print(f"   Generated: {len(audio_data)} bytes, {sample_count} samples, {duration_ms:.1f}ms")

        print(f"\n✅ Generated {len(audio_samples)} test audio samples")
    else:
        print("🔧 Step 1: Skipping audio generation (TTS not available)")
        audio_samples = []
    
    # Test 2: Connect to backend and test real-time audio
    print("\n🌐 Step 2: Testing real-time audio pipeline...")
    uri = "ws://localhost:5010"
    
    try:
        async with websockets.connect(uri) as websocket:
            print(f"   Connected to {uri}")
            
            # Register as webapp user
            session_id = "audio_clarity_test"
            register_message = {
                "type": "store_user",
                "session": session_id,
                "data": {
                    "name": "Audio Clarity Test",
                    "mobile": "webapp_test",
                    "userId": session_id,
                    "sessionType": "call"
                }
            }
            
            await websocket.send(json.dumps(register_message))
            response = await websocket.recv()
            print(f"   Registration response: {json.loads(response)}")
            
            # Start AI call
            start_call_message = {
                "type": "start_ai_call",
                "session": session_id
            }
            await websocket.send(json.dumps(start_call_message))
            print("   Started AI call")
            
            # Send a test message to trigger AI response
            test_message = "Please say: The audio quality test is working perfectly with crystal clear speech."
            
            # Simulate audio input (send some dummy audio chunks)
            print("   Sending test audio input...")
            for i in range(5):
                dummy_audio = [128] * 1024  # Silence (8-bit PCM)
                audio_message = {
                    "type": "audio_chunk",
                    "session": session_id,
                    "data": dummy_audio
                }
                await websocket.send(json.dumps(audio_message))
                await asyncio.sleep(0.1)
            
            # Wait for AI response
            print("   Waiting for AI audio response...")
            audio_responses = []
            timeout_count = 0
            
            while timeout_count < 50:  # Wait up to 5 seconds
                try:
                    response = await asyncio.wait_for(websocket.recv(), timeout=0.1)
                    data = json.loads(response)
                    
                    if data.get("type") == "llm_answer":
                        audio_data = data.get("data", "")
                        if audio_data:
                            audio_responses.append(audio_data)
                            print(f"   📥 Received AI audio chunk: {len(audio_data)} base64 chars")
                            
                            # Analyze the audio chunk
                            try:
                                decoded_audio = base64.b64decode(audio_data)
                                print(f"      Decoded to {len(decoded_audio)} bytes")
                                
                                # Check audio format
                                if len(decoded_audio) >= 4:
                                    sample_count = len(decoded_audio) // 2
                                    duration_ms = (sample_count / 44100) * 1000
                                    print(f"      Audio: {sample_count} samples, {duration_ms:.1f}ms duration")
                                    
                                    # Analyze first few samples
                                    samples = []
                                    for i in range(0, min(10, len(decoded_audio)), 2):
                                        if i + 1 < len(decoded_audio):
                                            low_byte = decoded_audio[i]
                                            high_byte = decoded_audio[i + 1]
                                            sample = (high_byte << 8) | low_byte
                                            if sample >= 32768:
                                                sample -= 65536
                                            samples.append(sample)
                                    
                                    if samples:
                                        print(f"      Sample range: {min(samples)} to {max(samples)}")
                                        rms = (sum(s*s for s in samples) / len(samples)) ** 0.5
                                        print(f"      RMS level: {rms:.1f}")
                                        
                            except Exception as decode_error:
                                print(f"      ❌ Audio decode error: {decode_error}")
                                
                    else:
                        print(f"   📨 Other message: {data.get('type')}")
                        
                except asyncio.TimeoutError:
                    timeout_count += 1
                    
            if audio_responses:
                print(f"\n✅ Received {len(audio_responses)} audio response chunks")
                
                # Combine all audio chunks for analysis
                combined_audio = b""
                for chunk in audio_responses:
                    combined_audio += base64.b64decode(chunk)
                
                print(f"   Total audio: {len(combined_audio)} bytes")
                print(f"   Total samples: {len(combined_audio) // 2}")
                print(f"   Total duration: {(len(combined_audio) // 2) / 44100:.2f} seconds")
                
                # Save test audio for manual verification
                with open('/tmp/ai_response_clarity_test.raw', 'wb') as f:
                    f.write(combined_audio)
                print(f"   💾 Saved test audio to /tmp/ai_response_clarity_test.raw")
                
            else:
                print("   ❌ No audio responses received")
                
    except Exception as e:
        print(f"   ❌ WebSocket test failed: {e}")
    
    # Test 3: Generate test instructions for manual verification
    print("\n📋 Step 3: Manual Testing Instructions")
    print("=" * 50)
    print("1. Open http://localhost:1800 in your browser")
    print("2. Click 'Connect' to establish WebSocket connection")
    print("3. Click 'Start Call' to begin audio session")
    print("4. Speak clearly: 'Please test the audio clarity improvements'")
    print("5. Listen carefully to the AI response for:")
    print("   - Clear speech without distortion")
    print("   - Consistent volume levels")
    print("   - Reduced background noise")
    print("   - Natural speech quality")
    print("   - No clicks, pops, or artifacts")
    print("\n6. Test different speech types:")
    print("   - Ask for technical explanations")
    print("   - Request number sequences")
    print("   - Ask for tongue twisters")
    print("   - Test with quiet and loud responses")
    
    print("\n🔧 Audio Processing Improvements Applied:")
    print("- Optimized audio filters (gentler high/low pass)")
    print("- Speech enhancement filter (2kHz boost)")
    print("- Improved compressor settings")
    print("- Volume normalization")
    print("- Noise gate for background noise reduction")
    print("- De-clicking filter for artifact removal")
    print("- RMS-based gain adjustment")
    print("- Reduced audio queue latency")
    
    print("\n✅ Audio clarity test completed!")
    print("   Check browser console for detailed audio processing logs")

if __name__ == "__main__":
    asyncio.run(test_audio_clarity())
